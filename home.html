<!DOCTYPE html>
<html lang="nl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Common Sense Health House</title>
    <link rel="stylesheet" href="style.css">
    <style>
        /* Interactive Navigation Styles */
        .interactive-nav {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: 5;
        }

        .connection-lines {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: 1;
        }

        .nav-topic {
            position: absolute;
            pointer-events: all;
            z-index: 10;
            cursor: pointer;
            opacity: 0;
            transform: scale(0.8) translate(-50%, -50%);
            animation: topicFadeIn 0.8s ease forwards;
        }

        .nav-topic:nth-child(2) { animation-delay: 0.5s; }
        .nav-topic:nth-child(3) { animation-delay: 0.7s; }
        .nav-topic:nth-child(4) { animation-delay: 0.9s; }
        .nav-topic:nth-child(5) { animation-delay: 1.1s; }
        .nav-topic:nth-child(6) { animation-delay: 1.3s; }
        .nav-topic:nth-child(7) { animation-delay: 1.5s; }

        .topic-circle {
            width: 120px;
            height: 120px;
            border-radius: 50%;
            background: linear-gradient(135deg, rgba(46, 207, 211, 0.95), rgba(22, 159, 163, 0.95));
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 8px 32px rgba(46, 207, 211, 0.4);
            transition: all 0.3s ease;
            border: 2px solid rgba(46, 207, 211, 0.7);
            backdrop-filter: blur(10px);
        }

        .topic-circle span {
            color: white;
            font-weight: 600;
            font-size: 14px;
            text-align: center;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
            padding: 0 8px;
        }

        .nav-topic:hover .topic-circle {
            transform: scale(1.15);
            box-shadow: 0 12px 48px rgba(46, 207, 211, 0.6);
            background: linear-gradient(135deg, rgba(46, 207, 211, 1), rgba(22, 159, 163, 1));
        }

        @keyframes topicFadeIn {
            to {
                opacity: 1;
                transform: scale(1) translate(-50%, -50%);
            }
        }



        /* Floating content styling */
        .floating-content {
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            text-align: center;
            z-index: 2;
            max-width: 600px;
            padding: 2rem;
        }

        .typing-text {
            opacity: 0;
            font-size: 2.5rem;
            margin-bottom: 1rem;
            color: #2ecfd3;
            text-shadow: 0 2px 10px rgba(46,207,211,0.3);
        }

        .fade-in-text {
            opacity: 0;
            transform: translateY(20px);
            transition: all 0.6s ease;
            color: #7ad7db;
            font-size: 1.2rem;
            line-height: 1.5;
            text-shadow: 0 1px 3px rgba(0,0,0,0.3);
        }

        /* Scroll Progress Indicator */
        .scroll-progress {
            position: fixed;
            top: 0;
            left: 0;
            width: 0%;
            height: 3px;
            background: linear-gradient(90deg, #2ecfd3, #169fa3);
            z-index: 9999;
            transition: width 0.1s ease;
        }

        /* Loading Animation */
        .loading-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, #232432 0%, #1a1b28 50%, #232432 100%);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 10000;
            opacity: 1;
            transition: opacity 0.5s ease;
        }

        .loading-overlay.hidden {
            opacity: 0;
            pointer-events: none;
        }

        .loading-spinner {
            width: 60px;
            height: 60px;
            border: 3px solid rgba(46, 207, 211, 0.3);
            border-top: 3px solid #2ecfd3;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* Floating Background Elements */
        .floating-elements {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: -1;
            overflow: hidden;
        }

        .floating-element {
            position: absolute;
            width: 4px;
            height: 4px;
            background: rgba(46, 207, 211, 0.6);
            border-radius: 50%;
            animation: float 6s ease-in-out infinite;
        }

        @keyframes float {
            0%, 100% {
                transform: translateY(0px) rotate(0deg);
                opacity: 0.6;
            }
            50% {
                transform: translateY(-20px) rotate(180deg);
                opacity: 1;
            }
        }

        /* Fade in animations */
        .fade-in-visible {
            opacity: 1 !important;
            transform: translateY(0) !important;
        }

        /* Responsive adjustments */
        @media (max-width: 768px) {
            .topic-circle {
                width: 70px;
                height: 70px;
            }

            .topic-circle span {
                font-size: 10px;
            }

            .typing-text {
                font-size: 2rem;
            }

            .fade-in-text {
                font-size: 1rem;
            }
        }
    </style>
</head>
<body class="home-page">
    <header>
        <img src="assets/57a.PNG" alt="Common Sense Health Center Logo" class="logo">
        <button class="hamburger" onclick="toggleMenu()">
            <span></span>
            <span></span>
            <span></span>
        </button>
        <nav id="nav-menu">
            <ul>
                <li><a href="home.html" class="active">Home</a></li>
                <li><a href="index.html">Welkom</a></li>
                <li><a href="about.html">Over Mij</a></li>
                <li><a href="lymfe.html">Lymfesysteem</a></li>
                <li><a href="mylogenics.html">Mylogenics</a></li>
                <li><a href="trainingen.html">Trainingen</a></li>
                <li><a href="contact.html">Contact</a></li>
            </ul>
        </nav>
    </header>

    <!-- Loading Overlay -->
    <div class="loading-overlay" id="loadingOverlay">
        <div class="loading-spinner"></div>
    </div>

    <!-- Scroll Progress Indicator -->
    <div class="scroll-progress" id="scrollProgress"></div>

    <!-- Floating Background Elements -->
    <div class="floating-elements" id="floatingElements"></div>



    <main>
        <!-- Floating Text Content -->
        <div class="floating-content">
            <h1 class="typing-text">Common Sense Health House</h1>
            <p class="fade-in-text" data-delay="1">Ontdek de kracht van natuurlijke gezondheid en welzijn</p>
        </div>

                    <!-- Interactive Navigation Topics -->
            <div class="interactive-nav">
                <div class="nav-topic" data-page="index.html" style="top: 15%; right: 10%;">
                    <div class="topic-circle">
                        <span>Welkom</span>
                    </div>
                </div>

                <div class="nav-topic" data-page="about.html" style="top: 40%; right: 5%;">
                    <div class="topic-circle">
                        <span>Over Mij</span>
                    </div>
                </div>

                <div class="nav-topic" data-page="lymfe.html" style="top: 65%; right: 15%;">
                    <div class="topic-circle">
                        <span>Lymfesysteem</span>
                    </div>
                </div>

                <div class="nav-topic" data-page="mylogenics.html" style="top: 25%; right: 30%;">
                    <div class="topic-circle">
                        <span>Mylogenics</span>
                    </div>
                </div>

                <div class="nav-topic" data-page="trainingen.html" style="top: 55%; right: 40%;">
                    <div class="topic-circle">
                        <span>Trainingen</span>
                    </div>
                </div>

                <div class="nav-topic" data-page="contact.html" style="top: 10%; right: 35%;">
                    <div class="topic-circle">
                        <span>Contact</span>
                    </div>
                </div>
            </div>
    </main>

    <footer>
        <p>&copy; 2025 Common Sense Health House</p>
    </footer>

    <script>
        // Enhanced Interactive Features
        document.addEventListener('DOMContentLoaded', function() {
            // Initialize all interactive features
            initLoadingAnimation();
            initScrollProgress();
            initFloatingElements();
            initFadeInAnimations();
            initSmoothScrolling();
            initTypingEffect();
            initInteractiveNavigation();
        });

        // Loading Animation
        function initLoadingAnimation() {
            const loadingOverlay = document.getElementById('loadingOverlay');
            if (!loadingOverlay) return;

            window.addEventListener('load', () => {
                setTimeout(() => {
                    loadingOverlay.classList.add('hidden');
                    setTimeout(() => {
                        loadingOverlay.style.display = 'none';
                    }, 500);
                }, 800);
            });
        }

        // Scroll Progress Indicator
        function initScrollProgress() {
            const progressBar = document.getElementById('scrollProgress');
            if (!progressBar) return;

            window.addEventListener('scroll', () => {
                const scrolled = (window.scrollY / (document.documentElement.scrollHeight - window.innerHeight)) * 100;
                progressBar.style.width = scrolled + '%';
            });
        }

        // Floating Background Elements
        function initFloatingElements() {
            const container = document.getElementById('floatingElements');
            if (!container) return;

            const elementCount = 15;

            for (let i = 0; i < elementCount; i++) {
                const element = document.createElement('div');
                element.className = 'floating-element';
                element.style.left = Math.random() * 100 + '%';
                element.style.top = Math.random() * 100 + '%';
                element.style.animationDelay = Math.random() * 6 + 's';
                element.style.animationDuration = (4 + Math.random() * 4) + 's';
                container.appendChild(element);
            }
        }

        // Fade In Animations
        function initFadeInAnimations() {
            const observerOptions = {
                threshold: 0.1,
                rootMargin: '0px 0px -50px 0px'
            };

            const observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        entry.target.classList.add('fade-in-visible');
                    }
                });
            }, observerOptions);

            document.querySelectorAll('.fade-in').forEach(el => {
                observer.observe(el);
            });
        }

        // Smooth Scrolling
        function initSmoothScrolling() {
            document.querySelectorAll('a[href^="#"]').forEach(anchor => {
                anchor.addEventListener('click', function (e) {
                    e.preventDefault();
                    const target = document.querySelector(this.getAttribute('href'));
                    if (target) {
                        target.scrollIntoView({
                            behavior: 'smooth',
                            block: 'start'
                        });
                    }
                });
            });
        }

        // Typing Effect for Hero Title
        function initTypingEffect() {
            const typingText = document.querySelector('.typing-text');
            if (!typingText) return;

            const text = typingText.textContent;
            typingText.textContent = '';
            typingText.style.opacity = '1';

            let i = 0;
            const typeWriter = () => {
                if (i < text.length) {
                    typingText.textContent += text.charAt(i);
                    i++;
                    setTimeout(typeWriter, 50);
                }
            };

            setTimeout(typeWriter, 1000);

            // Fade in other text elements
            setTimeout(() => {
                document.querySelectorAll('.fade-in-text').forEach((element, index) => {
                    setTimeout(() => {
                        element.style.opacity = '1';
                        element.style.transform = 'translateY(0)';
                    }, index * 500);
                });
            }, 2000);
        }

        // Interactive Navigation System
        function initInteractiveNavigation() {
            const navTopics = document.querySelectorAll('.nav-topic');

            // Add click handlers to navigation topics
            navTopics.forEach(topic => {
                topic.addEventListener('click', function() {
                    const page = this.getAttribute('data-page');
                    if (page) {
                        // Add loading effect
                        this.style.transform = 'scale(0.9)';
                        setTimeout(() => {
                            window.location.href = page;
                        }, 200);
                    }
                });
            });
        }

        // Original Menu Functions
        function toggleMenu() {
            const hamburger = document.querySelector('.hamburger');
            const nav = document.getElementById('nav-menu');

            if (!hamburger || !nav) return;

            hamburger.classList.toggle('active');
            nav.classList.toggle('active');

            // Prevent body scroll when menu is open
            if (nav.classList.contains('active')) {
                document.body.style.overflow = 'hidden';
            } else {
                document.body.style.overflow = '';
            }
        }

        // Enhanced menu interactions
        document.addEventListener('DOMContentLoaded', function() {
            // Close menu when clicking on a link
            document.querySelectorAll('#nav-menu a').forEach(link => {
                link.addEventListener('click', () => {
                    const hamburger = document.querySelector('.hamburger');
                    const nav = document.getElementById('nav-menu');

                    if (hamburger && nav) {
                        hamburger.classList.remove('active');
                        nav.classList.remove('active');
                        document.body.style.overflow = '';
                    }
                });
            });

            // Close menu when clicking outside
            document.addEventListener('click', (e) => {
                const hamburger = document.querySelector('.hamburger');
                const nav = document.getElementById('nav-menu');

                if (hamburger && nav && !hamburger.contains(e.target) && !nav.contains(e.target)) {
                    hamburger.classList.remove('active');
                    nav.classList.remove('active');
                    document.body.style.overflow = '';
                }
            });
        });
    </script>
</body>
</html>
