<!DOCTYPE html>
<html lang="nl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Common Sense Health House</title>
    <link rel="stylesheet" href="style.css">
    <style>
        /* Interactive Navigation Styles */
        .interactive-nav {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100vh;
            pointer-events: none;
            z-index: 5;
        }

        .connection-lines {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: 1;
        }

        .nav-topic {
            position: absolute;
            pointer-events: all;
            z-index: 10;
            cursor: pointer;
            opacity: 0;
            transform: scale(0.8);
            animation: topicFadeIn 0.8s ease forwards;
        }

        .nav-topic:nth-child(2) { animation-delay: 0.5s; }
        .nav-topic:nth-child(3) { animation-delay: 0.7s; }
        .nav-topic:nth-child(4) { animation-delay: 0.9s; }
        .nav-topic:nth-child(5) { animation-delay: 1.1s; }
        .nav-topic:nth-child(6) { animation-delay: 1.3s; }
        .nav-topic:nth-child(7) { animation-delay: 1.5s; }

        .topic-circle {
            width: 120px;
            height: 120px;
            border-radius: 50%;
            background: linear-gradient(135deg, rgba(46, 207, 211, 0.9), rgba(22, 159, 163, 0.9));
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 8px 32px rgba(46, 207, 211, 0.3);
            transition: all 0.3s ease;
            border: 2px solid rgba(46, 207, 211, 0.5);
        }

        .topic-circle span {
            color: white;
            font-weight: 600;
            font-size: 14px;
            text-align: center;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
        }

        .nav-topic:hover .topic-circle {
            transform: scale(1.1);
            box-shadow: 0 12px 48px rgba(46, 207, 211, 0.5);
            background: linear-gradient(135deg, rgba(46, 207, 211, 1), rgba(22, 159, 163, 1));
        }

        @keyframes topicFadeIn {
            to {
                opacity: 1;
                transform: scale(1);
            }
        }

        /* Connection lines animation */
        .connection-line {
            stroke: #2ecfd3;
            stroke-width: 2;
            fill: none;
            opacity: 0.6;
            stroke-dasharray: 5,5;
            animation: dashMove 3s linear infinite;
        }

        @keyframes dashMove {
            0% {
                stroke-dashoffset: 0;
            }
            100% {
                stroke-dashoffset: 20;
            }
        }

        /* Hero content styling */
        .hero-content {
            text-align: center;
            padding: 2rem;
            position: relative;
            z-index: 2;
        }

        .typing-text {
            opacity: 0;
            font-size: 2.5rem;
            margin-bottom: 1rem;
        }

        .fade-in-text {
            opacity: 0;
            transform: translateY(20px);
            transition: all 0.6s ease;
        }

        /* Responsive adjustments */
        @media (max-width: 768px) {
            .topic-circle {
                width: 80px;
                height: 80px;
            }

            .topic-circle span {
                font-size: 11px;
            }

            .nav-topic {
                transform: scale(0.8);
            }

            .typing-text {
                font-size: 2rem;
            }
        }
    </style>
</head>
<body class="home-page">
    <header>
        <img src="assets/57a.PNG" alt="Common Sense Health Center Logo" class="logo">
        <button class="hamburger" onclick="toggleMenu()">
            <span></span>
            <span></span>
            <span></span>
        </button>
        <nav id="nav-menu">
            <ul>
                <li><a href="home.html" class="active">Home</a></li>
                <li><a href="index.html">Welkom</a></li>
                <li><a href="about.html">Over Mij</a></li>
                <li><a href="lymfe.html">Lymfesysteem</a></li>
                <li><a href="mylogenics.html">Mylogenics</a></li>
                <li><a href="trainingen.html">Trainingen</a></li>
                <li><a href="contact.html">Contact</a></li>
            </ul>
        </nav>
    </header>

    <!-- Loading Overlay -->
    <div class="loading-overlay" id="loadingOverlay">
        <div class="loading-spinner"></div>
    </div>

    <!-- Floating Background Elements -->
    <div class="floating-elements" id="floatingElements"></div>

    <!-- Fixed Background Lines (Single SVG) -->
    <svg class="animated-lines left" viewBox="0 0 700 1400" xmlns="http://www.w3.org/2000/svg" preserveAspectRatio="xMinYMid meet">
        <circle cx="0" cy="600" r="700" stroke="#2ecfd3" stroke-width="5" fill="none"/>
        <circle cx="0" cy="600" r="600" stroke="#2ecfd3" stroke-width="4" fill="none"/>
        <circle cx="0" cy="600" r="500" stroke="#2ecfd3" stroke-width="3.5" fill="none"/>
        <circle cx="0" cy="600" r="400" stroke="#2ecfd3" stroke-width="3" fill="none"/>
        <circle cx="0" cy="600" r="700" class="line-glow slow" fill="none"/>
        <circle cx="0" cy="600" r="600" class="line-glow accent" fill="none"/>
        <circle cx="0" cy="600" r="500" class="line-glow" fill="none"/>
        <circle cx="0" cy="600" r="400" class="line-glow fast" fill="none"/>
    </svg>

    <svg class="animated-lines bottom-right" viewBox="0 0 700 1400" xmlns="http://www.w3.org/2000/svg" preserveAspectRatio="xMaxYMid meet">
        <circle cx="700" cy="800" r="700" stroke="#2ecfd3" stroke-width="5" fill="none"/>
        <circle cx="700" cy="800" r="600" stroke="#2ecfd3" stroke-width="4" fill="none"/>
        <circle cx="700" cy="800" r="500" stroke="#2ecfd3" stroke-width="3.5" fill="none"/>
        <circle cx="700" cy="800" r="400" stroke="#2ecfd3" stroke-width="3" fill="none"/>
        <circle cx="700" cy="800" r="700" class="line-glow slow" fill="none"/>
        <circle cx="700" cy="800" r="600" class="line-glow accent" fill="none"/>
        <circle cx="700" cy="800" r="500" class="line-glow" fill="none"/>
        <circle cx="700" cy="800" r="400" class="line-glow fast" fill="none"/>
    </svg>

    <main>
        <section class="hero section">
            <div class="hero-content">
                <h1 class="typing-text">Common Sense Health House</h1>
                <p class="fade-in-text" data-delay="1">Ontdek de kracht van natuurlijke gezondheid en welzijn</p>
            </div>

            <!-- Interactive Navigation Topics -->
            <div class="interactive-nav">
                <svg class="connection-lines" width="100%" height="100%" viewBox="0 0 1200 800">
                    <!-- Lines will be drawn dynamically -->
                </svg>

                <div class="nav-topic" data-page="index.html" style="top: 15%; right: 20%;">
                    <div class="topic-circle">
                        <span>Welkom</span>
                    </div>
                </div>

                <div class="nav-topic" data-page="about.html" style="top: 35%; right: 10%;">
                    <div class="topic-circle">
                        <span>Over Mij</span>
                    </div>
                </div>

                <div class="nav-topic" data-page="lymfe.html" style="top: 60%; right: 25%;">
                    <div class="topic-circle">
                        <span>Lymfesysteem</span>
                    </div>
                </div>

                <div class="nav-topic" data-page="mylogenics.html" style="top: 25%; right: 45%;">
                    <div class="topic-circle">
                        <span>Mylogenics</span>
                    </div>
                </div>

                <div class="nav-topic" data-page="trainingen.html" style="top: 75%; right: 15%;">
                    <div class="topic-circle">
                        <span>Trainingen</span>
                    </div>
                </div>

                <div class="nav-topic" data-page="contact.html" style="top: 45%; right: 35%;">
                    <div class="topic-circle">
                        <span>Contact</span>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <footer>
        <p>&copy; 2025 Common Sense Health House</p>
    </footer>

    <script>
        // Enhanced Interactive Features
        document.addEventListener('DOMContentLoaded', function() {
            // Initialize all interactive features
            initLoadingAnimation();
            initScrollProgress();
            initFloatingElements();
            initFadeInAnimations();
            initSmoothScrolling();
            initTypingEffect();
            initInteractiveNavigation();
        });

        // Loading Animation
        function initLoadingAnimation() {
            const loadingOverlay = document.getElementById('loadingOverlay');

            window.addEventListener('load', () => {
                setTimeout(() => {
                    loadingOverlay.classList.add('hidden');
                    setTimeout(() => {
                        loadingOverlay.style.display = 'none';
                    }, 500);
                }, 800);
            });
        }

        // Scroll Progress Indicator
        function initScrollProgress() {
            const progressBar = document.createElement('div');
            progressBar.className = 'scroll-progress';
            document.body.appendChild(progressBar);

            window.addEventListener('scroll', () => {
                const scrolled = (window.scrollY / (document.documentElement.scrollHeight - window.innerHeight)) * 100;
                progressBar.style.width = scrolled + '%';
            });
        }

        // Floating Background Elements
        function initFloatingElements() {
            const container = document.getElementById('floatingElements');
            const elementCount = 15;

            for (let i = 0; i < elementCount; i++) {
                const element = document.createElement('div');
                element.className = 'floating-element';
                element.style.left = Math.random() * 100 + '%';
                element.style.top = Math.random() * 100 + '%';
                element.style.animationDelay = Math.random() * 6 + 's';
                element.style.animationDuration = (4 + Math.random() * 4) + 's';
                container.appendChild(element);
            }
        }

        // Fade In Animations
        function initFadeInAnimations() {
            const observerOptions = {
                threshold: 0.1,
                rootMargin: '0px 0px -50px 0px'
            };

            const observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        entry.target.classList.add('fade-in-visible');
                    }
                });
            }, observerOptions);

            document.querySelectorAll('.fade-in').forEach(el => {
                observer.observe(el);
            });
        }

        // Smooth Scrolling
        function initSmoothScrolling() {
            document.querySelectorAll('a[href^="#"]').forEach(anchor => {
                anchor.addEventListener('click', function (e) {
                    e.preventDefault();
                    const target = document.querySelector(this.getAttribute('href'));
                    if (target) {
                        target.scrollIntoView({
                            behavior: 'smooth',
                            block: 'start'
                        });
                    }
                });
            });
        }

        // Typing Effect for Hero Title
        function initTypingEffect() {
            const typingText = document.querySelector('.typing-text');
            if (!typingText) return;

            const text = typingText.textContent;
            typingText.textContent = '';
            typingText.style.opacity = '1';

            let i = 0;
            const typeWriter = () => {
                if (i < text.length) {
                    typingText.textContent += text.charAt(i);
                    i++;
                    setTimeout(typeWriter, 50);
                }
            };

            setTimeout(typeWriter, 1000);

            // Fade in other text elements
            setTimeout(() => {
                document.querySelectorAll('.fade-in-text').forEach((element, index) => {
                    setTimeout(() => {
                        element.style.opacity = '1';
                        element.style.transform = 'translateY(0)';
                    }, index * 500);
                });
            }, 2000);
        }

        // Interactive Navigation System
        function initInteractiveNavigation() {
            const navTopics = document.querySelectorAll('.nav-topic');
            const connectionLines = document.querySelector('.connection-lines');

            if (!connectionLines) return;

            // Draw connection lines from left side to each topic
            function drawConnectionLines() {
                const svgRect = connectionLines.getBoundingClientRect();
                const leftPoint = { x: 0, y: svgRect.height / 2 };

                navTopics.forEach((topic, index) => {
                    const topicRect = topic.getBoundingClientRect();
                    const svgRect = connectionLines.getBoundingClientRect();

                    const topicCenter = {
                        x: topicRect.left + topicRect.width / 2 - svgRect.left,
                        y: topicRect.top + topicRect.height / 2 - svgRect.top
                    };

                    // Create curved path from left to topic
                    const controlPoint1 = {
                        x: leftPoint.x + (topicCenter.x - leftPoint.x) * 0.3,
                        y: leftPoint.y
                    };
                    const controlPoint2 = {
                        x: leftPoint.x + (topicCenter.x - leftPoint.x) * 0.7,
                        y: topicCenter.y
                    };

                    const path = document.createElementNS('http://www.w3.org/2000/svg', 'path');
                    const pathData = `M ${leftPoint.x} ${leftPoint.y} C ${controlPoint1.x} ${controlPoint1.y}, ${controlPoint2.x} ${controlPoint2.y}, ${topicCenter.x} ${topicCenter.y}`;

                    path.setAttribute('d', pathData);
                    path.setAttribute('class', 'connection-line');
                    path.style.animationDelay = (index * 0.2) + 's';

                    connectionLines.appendChild(path);
                });
            }

            // Add click handlers to navigation topics
            navTopics.forEach(topic => {
                topic.addEventListener('click', function() {
                    const page = this.getAttribute('data-page');
                    if (page) {
                        // Add loading effect
                        this.style.transform = 'scale(0.9)';
                        setTimeout(() => {
                            window.location.href = page;
                        }, 200);
                    }
                });

                // Add hover effects for connection lines
                topic.addEventListener('mouseenter', function() {
                    const index = Array.from(navTopics).indexOf(this);
                    const lines = connectionLines.querySelectorAll('.connection-line');
                    if (lines[index]) {
                        lines[index].style.strokeWidth = '3';
                        lines[index].style.opacity = '1';
                        lines[index].style.filter = 'drop-shadow(0 0 8px #2ecfd3)';
                    }
                });

                topic.addEventListener('mouseleave', function() {
                    const index = Array.from(navTopics).indexOf(this);
                    const lines = connectionLines.querySelectorAll('.connection-line');
                    if (lines[index]) {
                        lines[index].style.strokeWidth = '2';
                        lines[index].style.opacity = '0.6';
                        lines[index].style.filter = 'none';
                    }
                });
            });

            // Draw lines after a delay to ensure elements are positioned
            setTimeout(drawConnectionLines, 2000);

            // Redraw on window resize
            window.addEventListener('resize', () => {
                connectionLines.innerHTML = '';
                setTimeout(drawConnectionLines, 100);
            });
        }

        // Original Menu Functions
        function toggleMenu() {
            const hamburger = document.querySelector('.hamburger');
            const nav = document.getElementById('nav-menu');

            hamburger.classList.toggle('active');
            nav.classList.toggle('active');

            // Prevent body scroll when menu is open
            if (nav.classList.contains('active')) {
                document.body.style.overflow = 'hidden';
            } else {
                document.body.style.overflow = '';
            }
        }

        // Enhanced menu interactions
        document.addEventListener('DOMContentLoaded', function() {
            // Close menu when clicking on a link
            document.querySelectorAll('#nav-menu a').forEach(link => {
                link.addEventListener('click', () => {
                    const hamburger = document.querySelector('.hamburger');
                    const nav = document.getElementById('nav-menu');

                    hamburger.classList.remove('active');
                    nav.classList.remove('active');
                    document.body.style.overflow = '';
                });
            });

            // Close menu when clicking outside
            document.addEventListener('click', (e) => {
                const hamburger = document.querySelector('.hamburger');
                const nav = document.getElementById('nav-menu');

                if (!hamburger.contains(e.target) && !nav.contains(e.target)) {
                    hamburger.classList.remove('active');
                    nav.classList.remove('active');
                    document.body.style.overflow = '';
                }
            });
        });
    </script>
</body>
</html>
