<!DOCTYPE html>
<html lang="nl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Common Sense Health House</title>
    <link rel="stylesheet" href="style.css">
    <!-- Enhanced styles for interactive features -->
    <style>
        /* Scroll Progress Indicator */
        .scroll-progress {
            position: fixed;
            top: 0;
            left: 0;
            width: 0%;
            height: 3px;
            background: linear-gradient(90deg, #2ecfd3, #169fa3);
            z-index: 9999;
            transition: width 0.1s ease;
        }

        /* Loading Animation */
        .loading-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, #232432 0%, #1a1b28 50%, #232432 100%);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 10000;
            opacity: 1;
            transition: opacity 0.5s ease;
        }

        .loading-overlay.hidden {
            opacity: 0;
            pointer-events: none;
        }

        .loading-spinner {
            width: 60px;
            height: 60px;
            border: 3px solid rgba(46, 207, 211, 0.3);
            border-top: 3px solid #2ecfd3;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* Enhanced Hero Animations */
        .hero-content {
            opacity: 0;
            transform: translateY(30px);
            animation: fadeInUp 1s ease 0.5s forwards;
        }

        .hero-images {
            opacity: 0;
            transform: scale(0.9);
            animation: scaleIn 1s ease 0.3s forwards;
        }

        @keyframes fadeInUp {
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes scaleIn {
            to {
                opacity: 1;
                transform: scale(1);
            }
        }

        /* Floating Background Elements */
        .floating-elements {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: -1;
            overflow: hidden;
        }

        .floating-element {
            position: absolute;
            width: 4px;
            height: 4px;
            background: rgba(46, 207, 211, 0.6);
            border-radius: 50%;
            animation: float 6s ease-in-out infinite;
        }

        @keyframes float {
            0%, 100% {
                transform: translateY(0px) rotate(0deg);
                opacity: 0.6;
            }
            50% {
                transform: translateY(-20px) rotate(180deg);
                opacity: 1;
            }
        }
    </style>
</head>
<body class="home-page">
    <header>
        <img src="assets/57a.PNG" alt="Common Sense Health Center Logo" class="logo">
        <button class="hamburger" onclick="toggleMenu()">
            <span></span>
            <span></span>
            <span></span>
        </button>
        <nav id="nav-menu">
            <ul>
                <li><a href="index.html">Welkom</a></li>
                <li><a href="about.html">Over Mij</a></li>
                <li><a href="lymfe.html">Lymfesysteem</a></li>
                <li><a href="mylogenics.html">Mylogenics</a></li>
                <li><a href="contact.html">Contact</a></li>
            </ul>
        </nav>
    </header>

    <!-- Loading Overlay -->
    <div class="loading-overlay" id="loadingOverlay">
        <div class="loading-spinner"></div>
    </div>

    <!-- Scroll Progress Indicator -->
    <div class="scroll-progress" id="scrollProgress"></div>

    <!-- Floating Background Elements -->
    <div class="floating-elements" id="floatingElements"></div>

    <!-- Fixed Background Lines (Single SVG) -->
    <svg class="animated-lines left" viewBox="0 0 700 1400" xmlns="http://www.w3.org/2000/svg" preserveAspectRatio="xMinYMid meet">
        <circle cx="0" cy="600" r="700" stroke="#2ecfd3" stroke-width="5" fill="none"/>
        <circle cx="0" cy="600" r="600" stroke="#2ecfd3" stroke-width="4" fill="none"/>
        <circle cx="0" cy="600" r="500" stroke="#2ecfd3" stroke-width="3.5" fill="none"/>
        <circle cx="0" cy="600" r="400" stroke="#2ecfd3" stroke-width="3" fill="none"/>
        <circle cx="0" cy="600" r="700" class="line-glow slow" fill="none"/>
        <circle cx="0" cy="600" r="600" class="line-glow accent" fill="none"/>
        <circle cx="0" cy="600" r="500" class="line-glow" fill="none"/>
        <circle cx="0" cy="600" r="400" class="line-glow fast" fill="none"/>
    </svg>

    <svg class="animated-lines bottom-right" viewBox="0 0 700 1400" xmlns="http://www.w3.org/2000/svg" preserveAspectRatio="xMaxYMid meet">
        <circle cx="700" cy="800" r="700" stroke="#2ecfd3" stroke-width="5" fill="none"/>
        <circle cx="700" cy="800" r="600" stroke="#2ecfd3" stroke-width="4" fill="none"/>
        <circle cx="700" cy="800" r="500" stroke="#2ecfd3" stroke-width="3.5" fill="none"/>
        <circle cx="700" cy="800" r="400" stroke="#2ecfd3" stroke-width="3" fill="none"/>
        <circle cx="700" cy="800" r="700" class="line-glow slow" fill="none"/>
        <circle cx="700" cy="800" r="600" class="line-glow accent" fill="none"/>
        <circle cx="700" cy="800" r="500" class="line-glow" fill="none"/>
        <circle cx="700" cy="800" r="400" class="line-glow fast" fill="none"/>
    </svg>

    <main>
        <section class="hero section">
            <div class="hero-images" style="display: flex; justify-content: center; align-items: center; gap: 2em; margin-bottom: 2em; flex-wrap: wrap;">
                <div class="image-container">
                    <img src="assets\Afbeelding van WhatsApp op 2025-03-21 om 13.13.30_03059b88.png" alt="Home Photo" class="hero-photo interactive-image" style="max-width:340px; width:100%; border-radius:1.5em; box-shadow:0 4px 32px rgba(46,207,211,0.13);">
                </div>
                <div class="image-container">
                    <img src="assets/57a.PNG" alt="Common Sense Health Center Logo" class="hero-logo interactive-image" width="340" height="auto" />
                </div>
            </div>
            <div class="hero-content">
                <h1 class="typing-text">Welkom bij Common Sense Health House</h1>
                <p class="fade-in-text" data-delay="1">Voor een gezond Lichaam &amp; Brein, met coach Samantha.</p>
                <p class="fade-in-text" data-delay="2">Professionele begeleiding naar een vitaal leven door ervaring en geleerde kennis over de connectie en samenwerking van het menselijk lichaam en brein, algemene gezondheid en darmgezondheid, goede beweging, gezonde voeding en het (g)lymfatische systeem.</p>
                <p class="fade-in-text" data-delay="3">Voor Smallgroup outdoor trainingen en indoor behandelingen met veel selfcare technieken bent u bij CSHH aan het juiste adres.</p>
                <a href="about.html" class="cta enhanced-cta" data-delay="4">
                    <span>Ontdek meer</span>
                    <div class="cta-ripple"></div>
                </a>
            </div>
        </section>
    </main>

    <footer>
        <p>&copy; 2025 Common Sense Health House</p>
    </footer>

    <script>
        // Enhanced Interactive Features
        document.addEventListener('DOMContentLoaded', function() {
            // Initialize all interactive features
            initLoadingAnimation();
            initScrollProgress();
            initFloatingElements();
            initTypingEffect();
            initFadeInAnimations();
            initInteractiveImages();
            initEnhancedCTA();
            initSmoothScrolling();
            // initParallaxEffect(); // Disabled to fix background lines
        });

        // Loading Animation
        function initLoadingAnimation() {
            const loadingOverlay = document.getElementById('loadingOverlay');

            window.addEventListener('load', () => {
                setTimeout(() => {
                    loadingOverlay.classList.add('hidden');
                    setTimeout(() => {
                        loadingOverlay.style.display = 'none';
                    }, 500);
                }, 800);
            });
        }

        // Scroll Progress Indicator
        function initScrollProgress() {
            const scrollProgress = document.getElementById('scrollProgress');

            window.addEventListener('scroll', () => {
                const scrollTop = window.pageYOffset;
                const docHeight = document.body.scrollHeight - window.innerHeight;
                const scrollPercent = (scrollTop / docHeight) * 100;
                scrollProgress.style.width = scrollPercent + '%';
            });
        }

        // Floating Background Elements
        function initFloatingElements() {
            const container = document.getElementById('floatingElements');
            const elementCount = 15;

            for (let i = 0; i < elementCount; i++) {
                const element = document.createElement('div');
                element.className = 'floating-element';
                element.style.left = Math.random() * 100 + '%';
                element.style.top = Math.random() * 100 + '%';
                element.style.animationDelay = Math.random() * 6 + 's';
                element.style.animationDuration = (4 + Math.random() * 4) + 's';
                container.appendChild(element);
            }
        }

        // Typing Effect for Hero Title
        function initTypingEffect() {
            const typingText = document.querySelector('.typing-text');
            if (!typingText) return;

            const text = typingText.textContent;
            typingText.textContent = '';
            typingText.style.opacity = '1';

            let i = 0;
            const typeWriter = () => {
                if (i < text.length) {
                    typingText.textContent += text.charAt(i);
                    i++;
                    setTimeout(typeWriter, 50);
                }
            };

            setTimeout(typeWriter, 1000);
        }

        // Fade-in Animations for Text
        function initFadeInAnimations() {
            const fadeElements = document.querySelectorAll('.fade-in-text, .enhanced-cta');

            fadeElements.forEach(element => {
                const delay = element.getAttribute('data-delay') || 0;
                setTimeout(() => {
                    element.style.opacity = '1';
                    element.style.transform = 'translateY(0)';
                }, (delay * 500) + 1500);
            });
        }

        // Interactive Images with Hover Effects
        function initInteractiveImages() {
            const images = document.querySelectorAll('.interactive-image');

            images.forEach(img => {
                img.addEventListener('mouseenter', () => {
                    img.style.transform = 'scale(1.05) rotate(1deg)';
                    img.style.boxShadow = '0 8px 40px rgba(46,207,211,0.25)';
                });

                img.addEventListener('mouseleave', () => {
                    img.style.transform = 'scale(1) rotate(0deg)';
                    img.style.boxShadow = '0 4px 32px rgba(46,207,211,0.13)';
                });
            });
        }

        // Enhanced CTA Button with Ripple Effect
        function initEnhancedCTA() {
            const ctaButtons = document.querySelectorAll('.enhanced-cta');

            ctaButtons.forEach(button => {
                button.addEventListener('click', function(e) {
                    const ripple = this.querySelector('.cta-ripple');
                    const rect = this.getBoundingClientRect();
                    const size = Math.max(rect.width, rect.height);
                    const x = e.clientX - rect.left - size / 2;
                    const y = e.clientY - rect.top - size / 2;

                    ripple.style.width = ripple.style.height = size + 'px';
                    ripple.style.left = x + 'px';
                    ripple.style.top = y + 'px';
                    ripple.classList.add('active');

                    setTimeout(() => {
                        ripple.classList.remove('active');
                    }, 600);
                });
            });
        }

        // Smooth Scrolling for Navigation Links
        function initSmoothScrolling() {
            document.querySelectorAll('a[href^="#"]').forEach(anchor => {
                anchor.addEventListener('click', function (e) {
                    e.preventDefault();
                    const target = document.querySelector(this.getAttribute('href'));
                    if (target) {
                        target.scrollIntoView({
                            behavior: 'smooth',
                            block: 'start'
                        });
                    }
                });
            });
        }

        // Parallax Effect for Background Elements (Disabled to fix positioning)
        // function initParallaxEffect() {
        //     window.addEventListener('scroll', () => {
        //         const scrolled = window.pageYOffset;
        //         const parallaxElements = document.querySelectorAll('.animated-lines');
        //
        //         parallaxElements.forEach(element => {
        //             const speed = 0.5;
        //             element.style.transform = `translateY(${scrolled * speed}px)`;
        //         });
        //     });
        // }

        // Original Menu Functions (Enhanced)
        function toggleMenu() {
            const hamburger = document.querySelector('.hamburger');
            const nav = document.getElementById('nav-menu');

            hamburger.classList.toggle('active');
            nav.classList.toggle('active');

            // Add body scroll lock when menu is open
            if (nav.classList.contains('active')) {
                document.body.style.overflow = 'hidden';
            } else {
                document.body.style.overflow = '';
            }
        }

        // Enhanced menu interactions
        document.addEventListener('DOMContentLoaded', function() {
            // Close menu when clicking on a link
            document.querySelectorAll('#nav-menu a').forEach(link => {
                link.addEventListener('click', () => {
                    const hamburger = document.querySelector('.hamburger');
                    const nav = document.getElementById('nav-menu');

                    hamburger.classList.remove('active');
                    nav.classList.remove('active');
                    document.body.style.overflow = '';
                });
            });

            // Close menu when clicking outside
            document.addEventListener('click', (e) => {
                const hamburger = document.querySelector('.hamburger');
                const nav = document.getElementById('nav-menu');

                if (!hamburger.contains(e.target) && !nav.contains(e.target)) {
                    hamburger.classList.remove('active');
                    nav.classList.remove('active');
                    document.body.style.overflow = '';
                }
            });
        });
    </script>
</body>
</html>
