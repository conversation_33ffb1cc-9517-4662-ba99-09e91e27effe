@import url('https://fonts.googleapis.com/css2?family=Montserrat:wght@400;600;700&display=swap');

html, body {
    height: 100%;
    margin: 0;
    padding: 0;
    font-family: 'Montserrat', 'Segoe UI', Aria<PERSON>, sans-serif;
    background: linear-gradient(135deg, #232432 0%, #1a1b28 50%, #232432 100%);
    color: #eaf6f6;
    overflow-x: hidden;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

body {
    display: flex;
    flex-direction: column;
    min-height: 100vh;
    will-change: transform;
    position: relative;
}

body::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: 
        radial-gradient(circle at 20% 80%, rgba(46,207,211,0.03) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(46,207,211,0.02) 0%, transparent 50%);
    pointer-events: none;
    z-index: -1;
}

main {
    flex: 1;
    position: relative;
    z-index: 1;
    backface-visibility: hidden;
}

/* Header / Navbar */
header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: rgba(35,36,50, 0.95);
    padding: 20px 60px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    position: sticky;
    top: 0;
    z-index: 1000;
    backdrop-filter: blur(5px);
    -webkit-backdrop-filter: blur(5px);
    transform: translateZ(0);
}

.logo {
    height: 70px;
    transform: translateZ(0);
}

nav ul {
    display: flex;
    list-style: none;
    gap: 24px;
    margin: 0;
    padding: 0;
}

nav a {
    color: #2ecfd3;
    text-decoration: none;
    font-size: 1.1em;
    font-weight: 600;
    padding: 8px 20px;
    border-radius: 1.2em;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    transform: translateZ(0);
}

nav a:hover, nav a.active {
    background: linear-gradient(90deg, #2ecfd3, #169fa3);
    color: #fff;
    box-shadow: 0 2px 8px rgba(46, 207, 211, 0.3);
}

/* Hamburger menu button */
.hamburger {
    display: none;
    flex-direction: column;
    cursor: pointer;
    padding: 5px;
    background: none;
    border: none;
    z-index: 1001;
}

.hamburger span {
    width: 25px;
    height: 3px;
    background: #2ecfd3;
    margin: 3px 0;
    transition: 0.3s;
    border-radius: 2px;
}

.hamburger.active span:nth-child(1) {
    transform: rotate(-45deg) translate(-5px, 6px);
}

.hamburger.active span:nth-child(2) {
    opacity: 0;
}

.hamburger.active span:nth-child(3) {
    transform: rotate(45deg) translate(-5px, -6px);
}

/* Hero Section */
.hero {
    text-align: center;
    padding: 100px 20px 60px;
    background: radial-gradient(circle at 60% 40%, rgba(46,207,211,0.15) 0%, #232432 100%);
    border-radius: 2em;
}

.hero h1 {
    font-size: 3.2em;
    color: #2ecfd3;
    margin-bottom: 0.5em;
    font-weight: 700;
}

.hero p {
    font-size: 1.4em;
    max-width: 700px;
    margin: 0 auto 1.2em auto;
    font-weight: 500;
    color: #eaf6f6;
}

.cta {
    display: inline-block;
    background: #2ecfd3;
    color: #232432;
    padding: 12px 34px;
    border-radius: 30px;
    font-weight: bold;
    font-size: 1.15em;
    text-decoration: none;
    transition: background 0.2s, color 0.2s;
}

.cta:hover {
    background: #fff;
    color: #2ecfd3;
}

/* Animated Lines */
.animated-lines {
    position: absolute;
    z-index: 0;
    width: 60vw;
    min-width: 350px;
    max-width: 700px;
    pointer-events: none;
    opacity: 0.25;
}

.animated-lines.left {
    top: 120px; /* Below navbar */
    left: 0;
    animation: fadeInLeft 1.7s ease;
}

.animated-lines.right {
    bottom: 0;
    right: 0;
    transform: scaleX(-1);
    animation: fadeInRight 1.7s ease;
}

.animated-lines.bottom-right {
    position: fixed;
    bottom: -200px;
    right: -100px;
    width: 80vw;
    min-width: 500px;
    max-width: 900px;
    pointer-events: none;
    opacity: 0;
    z-index: 0;
    transform: scale(-1, -1);
    animation: fadeInRight 2s ease 0.3s forwards;
}

.animated-lines path {
    stroke-dasharray: 1200;
    stroke-dashoffset: 1200;
    animation: drawLine 2.5s 0.5s forwards, floatLine 6s ease-in-out infinite, pulseLine 2.5s 3s infinite;
}

.animated-lines .line-glow {
    stroke: #fff;
    stroke-width: 10;
    filter: blur(6px) brightness(2);
    opacity: 0;
    stroke-dasharray: 1200;
    stroke-dashoffset: 1200;
    animation: moveGlow 3.5s 3s linear infinite;
}

.animated-lines .line-glow.slow {
    animation-duration: 6s;
    stroke-width: 14;
    stroke: #7ad7db;
    filter: blur(10px) brightness(2.5);
}

.animated-lines .line-glow.fast {
    animation-duration: 2s;
    stroke-width: 7;
    stroke: #2ecfd3;
    filter: blur(4px) brightness(1.7);
}

.animated-lines .line-glow.accent {
    stroke: #169fa3;
    stroke-width: 12;
    filter: blur(8px) brightness(2.2);
    animation-duration: 4.5s;
}

@keyframes drawLine {
    to {
        stroke-dashoffset: 0;
    }
}

@keyframes floatLine {
    0%, 100% { filter: blur(0px); }
    50% { filter: blur(2.5px); }
}

@keyframes fadeInLeft {
    from { opacity: 0; transform: translateX(-80px); }
    to { opacity: 0.25; transform: none; }
}

@keyframes fadeInRight {
    from { 
        opacity: 0; 
        transform: scale(-1, -1) translateX(80px); 
    }
    to { 
        opacity: 0.25; 
        transform: scale(-1, -1); 
    }
}

@keyframes pulseLine {
    0%, 100% {
        filter: drop-shadow(0 0 0px #2ecfd3);
        stroke: #2ecfd3;
    }
    50% {
        filter: drop-shadow(0 0 12px #2ecfd3);
        stroke: #7ad7db;
    }
}

@keyframes moveGlow {
    0% {
        stroke-dashoffset: 1200;
        opacity: 0.0;
    }
    10% {
        opacity: 1.0;
    }
    80% {
        opacity: 1.0;
    }
    100% {
        stroke-dashoffset: 0;
        opacity: 0.0;
    }
}

  
/* Footer */
footer {
    text-align: center;
    padding: 24px 0;
    background: linear-gradient(90deg, #232432, #169fa3);
    color: #7ad7db;
    font-size: 1em;
    letter-spacing: 1px;
    margin-top: 60px;
    border-radius: 2em 2em 0 0;
    box-shadow: 0 -2px 18px rgba(46,207,211,0.11);
}

/* Responsive Navigation */
@media (max-width: 768px) {
    header {
        flex-direction: row;
        padding: 15px 20px;
        position: relative;
    }

    .logo {
        height: 50px;
    }

    .hamburger {
        display: flex;
    }

    nav {
        position: absolute;
        top: 100%;
        left: 0;
        right: 0;
        background: rgba(35,36,50, 0.98);
        backdrop-filter: blur(10px);
        -webkit-backdrop-filter: blur(10px);
        transform: translateY(-100%);
        opacity: 0;
        visibility: hidden;
        transition: all 0.3s ease;
        box-shadow: 0 4px 20px rgba(0,0,0,0.2);
        border-radius: 0 0 1em 1em;
    }

    nav.active {
        transform: translateY(0);
        opacity: 1;
        visibility: visible;
    }

    nav ul {
        flex-direction: column;
        gap: 0;
        margin: 0;
        padding: 10px 0;
    }

    nav a {
        display: block;
        padding: 15px 25px;
        border-radius: 0;
        font-size: 1em;
        border-bottom: 1px solid rgba(46, 207, 211, 0.1);
    }

    nav a:last-child {
        border-bottom: none;
    }

    nav a:hover, nav a.active {
        background: rgba(46, 207, 211, 0.1);
        color: #2ecfd3;
        box-shadow: none;
    }

    .hero {
        background: radial-gradient(circle at 60% 40%, rgba(46,207,211,0.08) 0%, rgba(35,36,50,0.85) 100%);
    }

    .hero h1 {
        font-size: 2.2em;
    }
    .hero p {
        font-size: 1.1em;
    }

    .animated-lines {
        width: 95vw;
        min-width: 120px;
        max-width: 350px;
        opacity: 0.55;
    }
    .animated-lines.bottom-right {
        min-width: 180px;
        max-width: 400px;
        bottom: -60px;
        right: -30px;
        opacity: 0.45;
    }
    .animated-lines path,
    .animated-lines .line-glow {
        opacity: 1 !important;
    }

    .section, .section-light, .section-dark {
        max-width: 98vw;
        width: 100%;
        margin-left: 0;
        margin-right: 0;
        box-sizing: border-box;
        padding-left: 0.5em;
        padding-right: 0.5em;
    }
    form {
        max-width: 270px;
        width: 100%;
        margin-left: auto;
        margin-right: auto;
        box-sizing: border-box;
    }
    form input, form textarea {
        max-width: 100%;
    }
}

/* --- Section Styling for Consistent Layouts --- */
.section {
    max-width: 760px;
    margin: 40px auto 0 auto;
    background: rgba(35,36,50,0.93);
    padding: 2.5em 2em;
    border-radius: 2em;
    box-shadow: 0 2px 32px rgba(46,207,211,0.10);
    position: relative;
    z-index: 1;
    text-align: left;
}
.section-light {
    background: rgba(255,255,255,0.92);
    color: #232432;
}
.section-dark {
    background: rgba(35,36,50,0.97);
    color: #eaf6f6;
}

/* --- Typography Improvements --- */
h1, h2, h3, h4, h5, h6 {
    font-family: 'Montserrat', 'Segoe UI', Arial, sans-serif;
    font-weight: 700;
    color: #2ecfd3;
    margin-top: 0;
    margin-bottom: 0.7em;
    letter-spacing: 0.01em;
}
h1 {
    font-size: 2.5em;
    line-height: 1.1;
}
h2 {
    font-size: 1.7em;
    color: #169fa3;
    margin-bottom: 0.5em;
}
h3 {
    font-size: 1.3em;
    color: #7ad7db;
}

p {
    font-size: 1.15em;
    line-height: 1.7;
    margin-bottom: 1.2em;
    color: inherit;
}

blockquote {
    border-left: 5px solid #2ecfd3;
    background: rgba(46,207,211,0.07);
    color: #2ecfd3;
    font-size: 1.3em;
    font-style: italic;
    margin: 2em 0;
    padding: 1em 1.5em;
    border-radius: 1em;
}

/* --- List Styling --- */
ul, ol {
    margin: 1em 0 1.5em 1.5em;
    padding: 0 0 0 1.2em;
    font-size: 1.08em;
    line-height: 1.7;
}
li {
    margin-bottom: 0.5em;
    padding-left: 0.2em;
}

/* --- Form Styling --- */
form {
    margin: 2em 0 1.5em 0;
    padding: 2em 1.5em;
    background: rgba(255,255,255,0.92);
    color: #232432;
    border-radius: 1.2em;
    box-shadow: 0 2px 24px rgba(46,207,211,0.10);
    max-width: 500px;
    margin-left: auto;
    margin-right: auto;
}
form label {
    display: block;
    margin-bottom: 0.4em;
    font-weight: 600;
    color: #169fa3;
}
form input, form textarea {
    max-width: 340px;
    width: 100%;
    margin-left: auto;
    margin-right: auto;
    display: block;
    border-radius: 0.7em;
    padding: 0.7em 1em;
    margin-bottom: 1.2em;
    border: 1.5px solid #2ecfd3;
    font-size: 1em;
    background: #f7fafd;
    color: #232432;
    transition: border 0.2s;
}
form input:focus, form textarea:focus {
    border-color: #169fa3;
    outline: none;
}
form button {
    background: #2ecfd3;
    color: #232432;
    border: none;
    padding: 0.8em 2.2em;
    border-radius: 1.2em;
    font-size: 1.1em;
    font-weight: bold;
    cursor: pointer;
    transition: background 0.2s, color 0.2s;
}
form button:hover {
    background: #169fa3;
    color: #fff;
}

/* --- Utility Classes --- */
.text-center { text-align: center; }
.text-right { text-align: right; }
.text-accent { color: #2ecfd3; }
.text-muted { color: #7ad7db; }


