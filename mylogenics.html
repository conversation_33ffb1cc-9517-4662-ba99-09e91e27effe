<!DOCTYPE html>
<html lang="nl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Mylogenics - Common Sense Health House</title>
    <link rel="stylesheet" href="style.css">
</head>
<body class="mylogenics-page">
    <header>
        <img src="assets/57a.PNG" alt="Common Sense Health Center Logo" class="logo">
        <button class="hamburger" onclick="toggleMenu()">
            <span></span>
            <span></span>
            <span></span>
        </button>
        <nav id="nav-menu">
            <ul>
                <li><a href="home.html">Home</a></li>
                <li><a href="index.html">Welkom</a></li>
                <li><a href="about.html">Over Mij</a></li>
                <li><a href="lymfe.html">Lymfesysteem</a></li>
                <li><a href="mylogenics.html" class="active">Mylogenics</a></li>
                <li><a href="trainingen.html">Trainingen</a></li>
                <li><a href="contact.html">Contact</a></li>
            </ul>
        </nav>
    </header>

    <!-- Scroll Progress Indicator -->
    <div class="scroll-progress" id="scrollProgress"></div>

    <!-- Floating Background Elements -->
    <div class="floating-elements" id="floatingElements"></div>

    <!-- Fixed Background Lines -->
    <svg class="animated-lines left" viewBox="0 0 700 1400" xmlns="http://www.w3.org/2000/svg" preserveAspectRatio="xMinYMid meet">
        <circle cx="0" cy="600" r="700" stroke="#2ecfd3" stroke-width="5" fill="none"/>
        <circle cx="0" cy="600" r="600" stroke="#2ecfd3" stroke-width="4" fill="none"/>
        <circle cx="0" cy="600" r="500" stroke="#2ecfd3" stroke-width="3.5" fill="none"/>
        <circle cx="0" cy="600" r="400" stroke="#2ecfd3" stroke-width="3" fill="none"/>
        <circle cx="0" cy="600" r="700" class="line-glow slow" fill="none"/>
        <circle cx="0" cy="600" r="600" class="line-glow accent" fill="none"/>
        <circle cx="0" cy="600" r="500" class="line-glow" fill="none"/>
        <circle cx="0" cy="600" r="400" class="line-glow fast" fill="none"/>
    </svg>

    <svg class="animated-lines bottom-right" viewBox="0 0 700 1400" xmlns="http://www.w3.org/2000/svg" preserveAspectRatio="xMaxYMid meet">
        <circle cx="700" cy="800" r="700" stroke="#2ecfd3" stroke-width="5" fill="none"/>
        <circle cx="700" cy="800" r="600" stroke="#2ecfd3" stroke-width="4" fill="none"/>
        <circle cx="700" cy="800" r="500" stroke="#2ecfd3" stroke-width="3.5" fill="none"/>
        <circle cx="700" cy="800" r="400" stroke="#2ecfd3" stroke-width="3" fill="none"/>
        <circle cx="700" cy="800" r="700" class="line-glow slow" fill="none"/>
        <circle cx="700" cy="800" r="600" class="line-glow accent" fill="none"/>
        <circle cx="700" cy="800" r="500" class="line-glow" fill="none"/>
        <circle cx="700" cy="800" r="400" class="line-glow fast" fill="none"/>
    </svg>

    <main>
        <section class="section">
            <h1>Mylogenics & Instant Muscle Release Technieken</h1>
            <p>Mylogenics is een behandelmethodiek die zich richt op het verhelpen van spier- en gewrichtspijn door middel van gerichte en gepersonaliseerde therapieën. Deze behandelingen combineren inzichten uit de fysiotherapie, osteopathie en manuele therapie om pijn te verlichten, de mobiliteit te verbeteren en de algehele spierfunctie te herstellen.</p>
            <p>Het doel van Mylogenics is om de oorzaak van klachten aan te pakken in plaats van alleen de symptomen te verlichten.</p>
        </section>
    </main>

    <footer>
        <p>&copy; 2025 Common Sense Health House</p>
    </footer>

    <script>
        // Enhanced Interactive Features
        document.addEventListener('DOMContentLoaded', function() {
            initScrollProgress();
            initFloatingElements();
            initFadeInAnimations();
        });

        // Scroll Progress Indicator
        function initScrollProgress() {
            const progressBar = document.getElementById('scrollProgress');
            if (!progressBar) return;

            window.addEventListener('scroll', () => {
                const scrolled = (window.scrollY / (document.documentElement.scrollHeight - window.innerHeight)) * 100;
                progressBar.style.width = scrolled + '%';
            });
        }

        // Floating Background Elements
        function initFloatingElements() {
            const container = document.getElementById('floatingElements');
            if (!container) return;

            const elementCount = 15;

            for (let i = 0; i < elementCount; i++) {
                const element = document.createElement('div');
                element.className = 'floating-element';
                element.style.left = Math.random() * 100 + '%';
                element.style.top = Math.random() * 100 + '%';
                element.style.animationDelay = Math.random() * 6 + 's';
                element.style.animationDuration = (4 + Math.random() * 4) + 's';
                container.appendChild(element);
            }
        }

        // Fade In Animations
        function initFadeInAnimations() {
            const observerOptions = {
                threshold: 0.1,
                rootMargin: '0px 0px -50px 0px'
            };

            const observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        entry.target.classList.add('fade-in-visible');
                    }
                });
            }, observerOptions);

            document.querySelectorAll('.fade-in').forEach(el => {
                observer.observe(el);
            });
        }

        // Menu Functions
        function toggleMenu() {
            const hamburger = document.querySelector('.hamburger');
            const nav = document.getElementById('nav-menu');

            if (!hamburger || !nav) return;

            hamburger.classList.toggle('active');
            nav.classList.toggle('active');

            // Prevent body scroll when menu is open
            if (nav.classList.contains('active')) {
                document.body.style.overflow = 'hidden';
            } else {
                document.body.style.overflow = '';
            }
        }

        // Enhanced menu interactions
        document.addEventListener('DOMContentLoaded', function() {
            // Close menu when clicking on a link
            document.querySelectorAll('#nav-menu a').forEach(link => {
                link.addEventListener('click', () => {
                    const hamburger = document.querySelector('.hamburger');
                    const nav = document.getElementById('nav-menu');

                    if (hamburger && nav) {
                        hamburger.classList.remove('active');
                        nav.classList.remove('active');
                        document.body.style.overflow = '';
                    }
                });
            });

            // Close menu when clicking outside
            document.addEventListener('click', (e) => {
                const hamburger = document.querySelector('.hamburger');
                const nav = document.getElementById('nav-menu');

                if (hamburger && nav && !hamburger.contains(e.target) && !nav.contains(e.target)) {
                    hamburger.classList.remove('active');
                    nav.classList.remove('active');
                    document.body.style.overflow = '';
                }
            });
        });
    </script>
</body>
</html>
