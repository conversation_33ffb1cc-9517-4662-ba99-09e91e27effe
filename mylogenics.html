<!DOCTYPE html>
<html lang="nl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Mylogenics - Common Sense Health House</title>
    <link rel="stylesheet" href="style.css">
</head>
<body class="mylogenics-page">
    <header>
        <img src="assets/57a.PNG" alt="Common Sense Health Center Logo" class="logo">
        <button class="hamburger" onclick="toggleMenu()">
            <span></span>
            <span></span>
            <span></span>
        </button>
        <nav id="nav-menu">
            <ul>
                <li><a href="index.html">Welkom</a></li>
                <li><a href="about.html">Over Mij</a></li>
                <li><a href="lymfe.html">Lymfesysteem</a></li>
                <li><a href="mylogenics.html" class="active">Mylogenics</a></li>
                <li><a href="contact.html">Contact</a></li>
            </ul>
        </nav>
    </header>
    <svg class="animated-lines" viewBox="0 0 1000 1000" xmlns="http://www.w3.org/2000/svg" preserveAspectRatio="xMidYMin meet">
        <path d="M0,700 A700,700 0 0,0 700,0" stroke="#2ecfd3" stroke-width="5" fill="none"/>
        <path d="M0,600 A600,600 0 0,0 600,0" stroke="#2ecfd3" stroke-width="4" fill="none"/>
        <path d="M0,500 A500,500 0 0,0 500,0" stroke="#2ecfd3" stroke-width="3.5" fill="none"/>
        <path d="M0,400 A400,400 0 0,0 400,0" stroke="#2ecfd3" stroke-width="3" fill="none"/>
        <path d="M0,700 A700,700 0 0,0 700,0" class="line-glow slow" fill="none"/>
        <path d="M0,600 A600,600 0 0,0 600,0" class="line-glow accent" fill="none"/>
        <path d="M0,500 A500,500 0 0,0 500,0" class="line-glow" fill="none"/>
        <path d="M0,400 A400,400 0 0,0 400,0" class="line-glow fast" fill="none"/>
    </svg>
    <svg class="animated-lines bottom-right" viewBox="0 0 1000 1000" xmlns="http://www.w3.org/2000/svg" preserveAspectRatio="xMidYMin meet">
        <path d="M0,700 A700,700 0 0,0 700,0" stroke="#2ecfd3" stroke-width="5" fill="none"/>
        <path d="M0,600 A600,600 0 0,0 600,0" stroke="#2ecfd3" stroke-width="4" fill="none"/>
        <path d="M0,500 A500,500 0 0,0 500,0" stroke="#2ecfd3" stroke-width="3.5" fill="none"/>
        <path d="M0,400 A400,400 0 0,0 400,0" stroke="#2ecfd3" stroke-width="3" fill="none"/>
        <path d="M0,700 A700,700 0 0,0 700,0" class="line-glow slow" fill="none"/>
        <path d="M0,600 A600,600 0 0,0 600,0" class="line-glow accent" fill="none"/>
        <path d="M0,500 A500,500 0 0,0 500,0" class="line-glow" fill="none"/>
        <path d="M0,400 A400,400 0 0,0 400,0" class="line-glow fast" fill="none"/>
    </svg>
    <main>
        <section class="section">
            <h1>Mylogenics & Instant Muscle Release Technieken</h1>
            <p>Mylogenics is een behandelmethodiek die zich richt op het verhelpen van spier- en gewrichtspijn door middel van gerichte en gepersonaliseerde therapieën. Deze behandelingen combineren inzichten uit de fysiotherapie, osteopathie en manuele therapie om pijn te verlichten, de mobiliteit te verbeteren en de algehele spierfunctie te herstellen.</p>
            <p>Het doel van Mylogenics is om de oorzaak van klachten aan te pakken in plaats van alleen de symptomen te verlichten.</p>
        </section>
    </main>
    <footer>
        <p>&copy; 2025 Common Sense Health House</p>
    </footer>

    <script>
        function toggleMenu() {
            const hamburger = document.querySelector('.hamburger');
            const nav = document.getElementById('nav-menu');
            
            hamburger.classList.toggle('active');
            nav.classList.toggle('active');
        }

        // Close menu when clicking on a link
        document.querySelectorAll('#nav-menu a').forEach(link => {
            link.addEventListener('click', () => {
                const hamburger = document.querySelector('.hamburger');
                const nav = document.getElementById('nav-menu');
                
                hamburger.classList.remove('active');
                nav.classList.remove('active');
            });
        });

        // Close menu when clicking outside
        document.addEventListener('click', (e) => {
            const hamburger = document.querySelector('.hamburger');
            const nav = document.getElementById('nav-menu');
            
            if (!hamburger.contains(e.target) && !nav.contains(e.target)) {
                hamburger.classList.remove('active');
                nav.classList.remove('active');
            }
        });
    </script>
</body>
</html>
