<!DOCTYPE html>
<html lang="nl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Trainingen - Common Sense Health House</title>
    <link rel="stylesheet" href="style.css">
</head>
<body class="trainingen-page">
    <header>
        <img src="assets/57a.PNG" alt="Common Sense Health Center Logo" class="logo">
        <button class="hamburger" onclick="toggleMenu()">
            <span></span>
            <span></span>
            <span></span>
        </button>
        <nav id="nav-menu">
            <ul>
                <li><a href="home.html">Home</a></li>
                <li><a href="index.html">Welkom</a></li>
                <li><a href="about.html">Over Mij</a></li>
                <li><a href="lymfe.html">Lymfesysteem</a></li>
                <li><a href="mylogenics.html">Mylogenics</a></li>
                <li><a href="trainingen.html" class="active">Trainingen</a></li>
                <li><a href="contact.html">Contact</a></li>
            </ul>
        </nav>
    </header>

    <!-- Fixed Background Lines (Single SVG) -->
    <svg class="animated-lines left" viewBox="0 0 700 1400" xmlns="http://www.w3.org/2000/svg" preserveAspectRatio="xMinYMid meet">
        <circle cx="0" cy="600" r="700" stroke="#2ecfd3" stroke-width="5" fill="none"/>
        <circle cx="0" cy="600" r="600" stroke="#2ecfd3" stroke-width="4" fill="none"/>
        <circle cx="0" cy="600" r="500" stroke="#2ecfd3" stroke-width="3.5" fill="none"/>
        <circle cx="0" cy="600" r="400" stroke="#2ecfd3" stroke-width="3" fill="none"/>
        <circle cx="0" cy="600" r="700" class="line-glow slow" fill="none"/>
        <circle cx="0" cy="600" r="600" class="line-glow accent" fill="none"/>
        <circle cx="0" cy="600" r="500" class="line-glow" fill="none"/>
        <circle cx="0" cy="600" r="400" class="line-glow fast" fill="none"/>
    </svg>

    <svg class="animated-lines bottom-right" viewBox="0 0 700 1400" xmlns="http://www.w3.org/2000/svg" preserveAspectRatio="xMaxYMid meet">
        <circle cx="700" cy="800" r="700" stroke="#2ecfd3" stroke-width="5" fill="none"/>
        <circle cx="700" cy="800" r="600" stroke="#2ecfd3" stroke-width="4" fill="none"/>
        <circle cx="700" cy="800" r="500" stroke="#2ecfd3" stroke-width="3.5" fill="none"/>
        <circle cx="700" cy="800" r="400" stroke="#2ecfd3" stroke-width="3" fill="none"/>
        <circle cx="700" cy="800" r="700" class="line-glow slow" fill="none"/>
        <circle cx="700" cy="800" r="600" class="line-glow accent" fill="none"/>
        <circle cx="700" cy="800" r="500" class="line-glow" fill="none"/>
        <circle cx="700" cy="800" r="400" class="line-glow fast" fill="none"/>
    </svg>

    <main>
        <section class="section section-light">
            <h1>Trainingen</h1>
            <p class="subtitle">Buiten, functioneel en effectief!</p>

            <div class="content-block">
                <p>
                    Of je nu net begint of al even bezig bent, je bent welkom zoals je bent. Geen massaproductie of het gevoel dat je in het diepe wordt gegooid, gewoon goede training, fijne mensen en echte resultaten.
                </p>

                <div class="highlight-box">
                    <h3>Small Group Outdoor Training</h3>
                    <p>
                        Train buiten, in een groep van maximaal 6 gemotiveerde mensen, samen met mij, coach Sam. Iedere les is anders, uitdagend en afgestemd op jouw persoonlijke niveau. Je werkt aan kracht, conditie, explosiviteit, mobiliteit, ademhaling, en dat allemaal lekker in de buitenlucht.
                    </p>
                </div>
            </div>
        </section>
    </main>

    <footer>
        <p>&copy; 2025 Common Sense Health House</p>
    </footer>

    <script>
        function toggleMenu() {
            const hamburger = document.querySelector('.hamburger');
            const nav = document.getElementById('nav-menu');

            hamburger.classList.toggle('active');
            nav.classList.toggle('active');
        }

        // Close menu when clicking on a link
        document.querySelectorAll('#nav-menu a').forEach(link => {
            link.addEventListener('click', () => {
                const hamburger = document.querySelector('.hamburger');
                const nav = document.getElementById('nav-menu');

                hamburger.classList.remove('active');
                nav.classList.remove('active');
            });
        });

        // Close menu when clicking outside
        document.addEventListener('click', (e) => {
            const hamburger = document.querySelector('.hamburger');
            const nav = document.getElementById('nav-menu');

            if (!hamburger.contains(e.target) && !nav.contains(e.target)) {
                hamburger.classList.remove('active');
                nav.classList.remove('active');
            }
        });
    </script>
</body>
</html>
