<!DOCTYPE html>
<html lang="nl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Trainingen - Common Sense Health House</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', sans-serif;
            background: linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 50%, #16213e 100%);
            color: #ffffff;
            line-height: 1.6;
            overflow-x: hidden;
            min-height: 100vh;
        }

        /* Navigation */
        nav {
            position: fixed;
            top: 0;
            width: 100%;
            background: rgba(10, 10, 10, 0.95);
            backdrop-filter: blur(10px);
            z-index: 1000;
            padding: 1rem 0;
            border-bottom: 1px solid rgba(46, 207, 211, 0.1);
        }

        .nav-container {
            max-width: 1200px;
            margin: 0 auto;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 2rem;
        }

        .logo {
            font-size: 1.5rem;
            font-weight: 700;
            color: #2ecfd3;
        }

        .nav-links {
            display: flex;
            list-style: none;
            gap: 2rem;
        }

        .nav-links a {
            color: #ffffff;
            text-decoration: none;
            font-weight: 500;
            transition: color 0.3s ease;
        }

        .nav-links a:hover {
            color: #2ecfd3;
        }

        /* Background Elements */
        .animated-lines {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: -2;
            opacity: 0.3;
            pointer-events: none;
        }

        .animated-lines.left {
            left: -50%;
        }

        .animated-lines.bottom-right {
            right: -50%;
            left: auto;
        }

        .line-glow {
            stroke: #2ecfd3;
            stroke-width: 2;
            filter: drop-shadow(0 0 8px rgba(46, 207, 211, 0.6));
            animation: pulseLine 4s ease-in-out infinite;
        }

        .line-glow.slow {
            animation-duration: 6s;
            animation-delay: 0s;
        }

        .line-glow.fast {
            animation-duration: 3s;
            animation-delay: 1s;
        }

        .line-glow.accent {
            animation-duration: 5s;
            animation-delay: 2s;
        }

        @keyframes pulseLine {
            0%, 100% { opacity: 0.3; filter: drop-shadow(0 0 8px rgba(46, 207, 211, 0.6)); }
            50% { opacity: 0.8; filter: drop-shadow(0 0 20px rgba(46, 207, 211, 0.9)); }
        }

        /* Main Content */
        .main-content {
            padding-top: 120px;
            max-width: 1200px;
            margin: 0 auto;
            padding-left: 2rem;
            padding-right: 2rem;
        }

        .page-header {
            text-align: center;
            margin-bottom: 4rem;
        }

        .page-title {
            font-size: 3rem;
            font-weight: 700;
            color: #2ecfd3;
            margin-bottom: 1rem;
        }

        .page-subtitle {
            font-size: 1.2rem;
            color: #b0b0b0;
            max-width: 600px;
            margin: 0 auto;
        }

        .training-section {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 20px;
            padding: 3rem;
            margin-bottom: 3rem;
            border: 1px solid rgba(46, 207, 211, 0.1);
            backdrop-filter: blur(10px);
        }

        .training-description {
            font-size: 1.1rem;
            line-height: 1.8;
            margin-bottom: 2rem;
            color: #e0e0e0;
        }

        .training-highlight {
            background: linear-gradient(135deg, rgba(46, 207, 211, 0.1), rgba(46, 207, 211, 0.05));
            border-left: 4px solid #2ecfd3;
            padding: 2rem;
            border-radius: 10px;
            margin: 2rem 0;
        }

        .training-highlight h3 {
            color: #2ecfd3;
            font-size: 1.4rem;
            margin-bottom: 1rem;
        }

        .training-highlight p {
            font-size: 1.1rem;
            line-height: 1.7;
        }

        @media (max-width: 768px) {
            .nav-links {
                display: none;
            }
            
            .page-title {
                font-size: 2rem;
            }
            
            .training-section {
                padding: 2rem;
            }
            
            .main-content {
                padding-left: 1rem;
                padding-right: 1rem;
            }
        }
    </style>
</head>
<body>
    <!-- Background SVG Elements -->
    <svg class="animated-lines left" viewBox="0 0 700 1400" xmlns="http://www.w3.org/2000/svg" preserveAspectRatio="xMinYMid meet">
        <circle cx="0" cy="600" r="700" stroke="#2ecfd3" stroke-width="5" fill="none"/>
        <circle cx="0" cy="600" r="600" stroke="#2ecfd3" stroke-width="4" fill="none"/>
        <circle cx="0" cy="600" r="500" stroke="#2ecfd3" stroke-width="3.5" fill="none"/>
        <circle cx="0" cy="600" r="400" stroke="#2ecfd3" stroke-width="3" fill="none"/>
        <circle cx="0" cy="600" r="700" class="line-glow slow" fill="none"/>
        <circle cx="0" cy="600" r="600" class="line-glow accent" fill="none"/>
        <circle cx="0" cy="600" r="500" class="line-glow" fill="none"/>
        <circle cx="0" cy="600" r="400" class="line-glow fast" fill="none"/>
    </svg>

    <svg class="animated-lines bottom-right" viewBox="0 0 700 1400" xmlns="http://www.w3.org/2000/svg" preserveAspectRatio="xMaxYMid meet">
        <circle cx="700" cy="800" r="700" stroke="#2ecfd3" stroke-width="5" fill="none"/>
        <circle cx="700" cy="800" r="600" stroke="#2ecfd3" stroke-width="4" fill="none"/>
        <circle cx="700" cy="800" r="500" stroke="#2ecfd3" stroke-width="3.5" fill="none"/>
        <circle cx="700" cy="800" r="400" stroke="#2ecfd3" stroke-width="3" fill="none"/>
        <circle cx="700" cy="800" r="700" class="line-glow slow" fill="none"/>
        <circle cx="700" cy="800" r="600" class="line-glow accent" fill="none"/>
        <circle cx="700" cy="800" r="500" class="line-glow" fill="none"/>
        <circle cx="700" cy="800" r="400" class="line-glow fast" fill="none"/>
    </svg>

    <!-- Navigation -->
    <nav>
        <div class="nav-container">
            <div class="logo">CSHH</div>
            <ul class="nav-links">
                <li><a href="index.html">Welkom</a></li>
                <li><a href="#over">Over Mij</a></li>
                <li><a href="#lymfesysteem">Lymfesysteem</a></li>
                <li><a href="#mylogenics">Mylogenics</a></li>
                <li><a href="trainingen.html">Trainingen</a></li>
                <li><a href="#contact">Contact</a></li>
            </ul>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="main-content">
        <div class="page-header">
            <h1 class="page-title">Trainingen</h1>
            <p class="page-subtitle">Buiten, functioneel en effectief!</p>
        </div>

        <div class="training-section">
            <p class="training-description">
                Of je nu net begint of al even bezig bent, je bent welkom zoals je bent. Geen massaproductie of het gevoel dat je in het diepe wordt gegooid, gewoon goede training, fijne mensen en echte resultaten.
            </p>

            <div class="training-highlight">
                <h3>Small Group Outdoor Training</h3>
                <p>
                    Train buiten, in een groep van maximaal 6 gemotiveerde mensen, samen met mij, coach Sam. Iedere les is anders, uitdagend en afgestemd op jouw persoonlijke niveau. Je werkt aan kracht, conditie, explosiviteit, mobiliteit, ademhaling, en dat allemaal lekker in de buitenlucht.
                </p>
            </div>
        </div>
    </div>
</body>
</html>
